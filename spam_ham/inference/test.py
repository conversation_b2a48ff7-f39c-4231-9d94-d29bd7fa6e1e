import torch

# This script assumes your model loading functions are in this file
from age_verification.model.model_defination import load_gemma3n_model_for_classification, load_gemma3n_processor

# --- Configuration ---
model_name = "/home/<USER>/Documents/Anas/age_verification_gemma3n/models/pretrained_gemma3n/gemma-3n-E2B"
num_labels = 2
lora_config_dict = {
    "r": 8, "lora_alpha": 16, "lora_dropout": 0.05, "bias": "none",
    "task_type": "SEQ_CLS", "target_modules": ["q_proj", "v_proj"]
}

def inspect_vocabulary():
    """
    Loads the model and tokenizer to inspect their vocabularies before and after our fix.
    """
    print("Loading model and processor...")
    model = load_gemma3n_model_for_classification(model_name, num_labels, lora_config_dict)
    processor = load_gemma3n_processor(model_name)
    print("...Loading complete.")
    
    # --- 2. Inspect the "BEFORE" State ---
    print("\n" + "="*50)
    print("--- STATE BEFORE FIX ---")
    
    # Get the vocabulary sizes using the CORRECT paths
    tokenizer_vocab_size_before = len(processor.tokenizer)
    # The embedding layer is inside the text model component
    embedding_layer_size_before = model.model.model.language_model.embed_tokens.weight.shape[0]

    print(f"Tokenizer vocabulary size: {tokenizer_vocab_size_before}")
    print(f"Model embedding layer size:  {embedding_layer_size_before}")

    # Check how the tokenizer interprets the "<image>" string
    image_str = "<image>"
    tokenized_id_before = processor.tokenizer.convert_tokens_to_ids(image_str)
    
    print(f"\nLooking up the string '{image_str}' in the tokenizer...")
    print(f"  - It maps to token ID: {tokenized_id_before}")
    print(f"  - The tokenizer's 'unknown token' ID is: {processor.tokenizer.unk_token_id}")

    if tokenized_id_before == processor.tokenizer.unk_token_id:
        print("\nDIAGNOSIS: The tokenizer does NOT recognize '<image>' as a special token.")
        print("This mismatch is the root cause of all downstream errors.")

if __name__ == "__main__":
    inspect_vocabulary()