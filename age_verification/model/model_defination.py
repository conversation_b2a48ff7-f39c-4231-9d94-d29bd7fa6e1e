import os
import json
import torch
import torch.nn as nn
from peft import get_peft_model, LoraConfig
from safetensors.torch import load_file

# Use local imports for all transformers components
from gemma3_local.src.transformers import AutoProcessor, Gemma3nConfig, TimmWrapperConfig, PretrainedConfig
from gemma3_local.src.transformers.models.gemma3n.configuration_gemma3n import Gemma3nTextConfig, Gemma3nAudioConfig
from gemma3_local.src.transformers.models.gemma3n.modeling_gemma3n import Gemma3nForSequenceClassification, Gemma3nTextModel, Gemma3nAudioEncoder
from gemma3_local.src.transformers.models.auto import AutoModel
from gemma3_local.src.transformers.models.timm_wrapper.modeling_timm_wrapper import TimmWrapperModel




def load_gemma3n_model_for_classification(
    model_name,
    num_labels,
    # lora_config_dict
):
    """
    The definitive loading function that combines monkey-patching with a robust,
    manual sharded checkpoint loader, avoiding meta tensors for maximum stability.
    """
    # --- Step 1: Prepare the Correct Configuration ---
    config = Gemma3nConfig.from_pretrained(model_name)
    if isinstance(config.vision_config, PretrainedConfig):
        vision_config_dict = config.vision_config.to_dict()
        config.vision_config = TimmWrapperConfig(**vision_config_dict)
    config.num_labels = num_labels
    print("--- Model Definition: Final Direct Instantiation Approach ---")
    print("Step 1: Corrected config prepared.")

    # --- Step 2: The COMPLETE Monkey Patch ---
    original_from_config = AutoModel.from_config

    def patched_from_config(config, **kwargs):
        if isinstance(config, TimmWrapperConfig): return TimmWrapperModel(config)
        elif isinstance(config, Gemma3nTextConfig): return Gemma3nTextModel(config)
        elif isinstance(config, Gemma3nAudioConfig): return Gemma3nAudioEncoder(config)
        else: return original_from_config(config, **kwargs)
    AutoModel.from_config = patched_from_config
    print("Step 2: Complete AutoModel.from_config has been patched.")

    model = None
    try:
        # --- Step 3: Instantiate the Full Model on CPU ---
        # This creates the full model with randomly initialized weights, avoiding 'meta' tensors entirely.
        print("Step 3: Instantiating full model on CPU...")
        model = Gemma3nForSequenceClassification(config)
        print("...Full model created successfully.")

        # --- Step 4: Manually Load Sharded Weights ---
        print("Step 4: Manually loading sharded weights to overwrite random weights...")
        index_path = os.path.join(model_name, "model.safetensors.index.json")
        if not os.path.exists(index_path):
            raise FileNotFoundError(f"Cannot find the model index file at {index_path}")

        with open(index_path, 'r') as f:
            index = json.load(f)

        full_state_dict = {}
        shard_files = sorted(list(set(index["weight_map"].values())))

        for shard_file in shard_files:
            shard_path = os.path.join(model_name, shard_file)
            shard_state_dict = load_file(shard_path, device="cpu")
            full_state_dict.update(shard_state_dict)
            del shard_state_dict

        # Load the complete state dict. `strict=False` is needed for the classification head mismatch.
        model.load_state_dict(full_state_dict, strict=False)
        print("...Sharded weights loaded successfully!")
        
        # # --- Step 5: Apply LoRA ---
        # lora_config = LoraConfig(**lora_config_dict)
        # model = get_peft_model(model, lora_config)
        # print("Step 5: LoRA config applied.")
        # model.print_trainable_parameters()

    finally:
        # --- Step 6: Restore the Original Function ---
        AutoModel.from_config = original_from_config
        print("Step 6: Original AutoModel.from_config restored.")
    
    # Return the complete model, which is on the CPU
    return model


def load_gemma3n_processor(model_name):
    """Load the pre-trained Gemma3n processor."""
    processor = AutoProcessor.from_pretrained(model_name)
    return processor