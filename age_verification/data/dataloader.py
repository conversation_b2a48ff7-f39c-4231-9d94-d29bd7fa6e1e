# In age_verification/data/dataloader.py

import torch
from torch.utils.data import DataLoader


def create_face_dataloaders(dataset, processor, batch_size, shuffle=True):
    """
    Creates a DataLoader with the definitive collate_fn that correctly handles
    a batch of dictionaries from the dataset.
    """

    def collate_fn(batch):
        """
        Correctly unnpacks a list of dictionaries into a single batch dictionary.

        Args:
            batch (list): A list of dictionaries, where each is a sample from the Dataset.
                          e.g., [{'image': img, 'text': txt, 'label': lbl}, ...]
        
        Returns:
            dict: A single batch dictionary ready for the model.
        """
        # --- The Correct Implementation ---
        # 1. Initialize empty lists to hold the components of the batch.
        images = []
        texts = []
        labels = []

        # 2. Loop through each dictionary in the batch and append its components by KEY.
        for item in batch:
            images.append(item["image"])  # Access by key "image"
            texts.append(item["text"])    # Access by key "text"
            labels.append(item["label"])  # Access by key "label"

        # Manually process list of images into a single tensor
        # THis uses the reliable sub-component and avoids the buggy logic
        image_inputs = processor.image_processor(images, return_tensors="pt")

        # Tokenize the text using the reliable sub-component
        text_inputs = processor.tokenizer(
            text=texts,
            return_tensors="pt",
            padding=True,
            truncation=True,
        )
        
        # 3. Use the processor to tokenize the texts and preprocess the images.
        processed_inputs = {
            "input_ids": text_inputs["input_ids"],
            "attention_mask": text_inputs["attention_mask"],
            "pixel_values": image_inputs["pixel_values"],
            "labels": torch.tensor(labels, dtype=torch.long)
        }

        # 4. Add the labels to the final batch dictionary.
        # processed_inputs["labels"] = torch.tensor(labels, dtype=torch.long)
        
        return processed_inputs

    # --- Create and return the DataLoader ---
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        collate_fn=collate_fn,
        num_workers=4,
        pin_memory=True,
    )